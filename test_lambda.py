#!/usr/bin/env python3
"""
Test script for the Georgia DLR Portal Lambda function
"""

import json
from lambda_function import lambda_handler

def test_lambda_function():
    """
    Test the lambda function with sample data
    """
    
    # Test event - replace with actual deal number you want to search
    test_event = {
        'deal_number': '12345',  # Replace with actual deal number
        'username': 'abi34343@232',
        'password': 'ddokeee'
    }
    
    # Mock context (not used in our function)
    class MockContext:
        def __init__(self):
            self.function_name = 'test-function'
            self.memory_limit_in_mb = 128
            self.invoked_function_arn = 'arn:aws:lambda:us-east-1:123456789012:function:test-function'
            self.aws_request_id = 'test-request-id'
    
    context = MockContext()
    
    print("Testing Georgia DLR Portal Lambda Function...")
    print(f"Test event: {json.dumps(test_event, indent=2)}")
    print("-" * 50)
    
    try:
        # Call the lambda function
        result = lambda_handler(test_event, context)
        
        print("Lambda function result:")
        print(json.dumps(result, indent=2))
        
        # Parse and display the result
        if result['statusCode'] == 200:
            body = json.loads(result['body'])
            print("\n✅ SUCCESS!")
            print(f"Deals found: {body.get('deals_found', 0)}")
            if body.get('deals'):
                print("\nDeal details:")
                for i, deal in enumerate(body['deals'], 1):
                    print(f"  Deal {i}: {deal}")
        else:
            body = json.loads(result['body'])
            print(f"\n❌ ERROR: {body.get('error', 'Unknown error')}")
            print(f"Message: {body.get('message', 'No message')}")
            
    except Exception as e:
        print(f"\n💥 EXCEPTION: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_lambda_function()
