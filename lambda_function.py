import json
import requests
from bs4 import BeautifulSoup
import time
import re

class GeorgiaDLRPortal:
    def __init__(self):
        self.session = requests.Session()
        self.base_url = "https://georgia.dlrdmv.com"
        self.login_url = f"{self.base_url}/#!/dashboard/"

        # Set headers to mimic a real browser
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })

    def login(self, username, password):
        """
        Login to the Georgia DLR portal
        """
        try:
            # First, get the login page to extract any necessary tokens/forms
            print("Getting login page...")
            response = self.session.get(self.base_url)

            if response.status_code != 200:
                return False, f"Failed to access login page. Status code: {response.status_code}"

            # Parse the HTML to find login form details
            soup = BeautifulSoup(response.text, 'html.parser')

            # Look for login form - this might be an Angular app, so we need to handle it differently
            # Since this appears to be an Angular single-page application, we'll need to make API calls

            # Try to find the actual login endpoint by examining the page
            login_data = {
                'username': username,
                'password': password
            }

            # Common login endpoints for such portals
            possible_login_endpoints = [
                f"{self.base_url}/api/login",
                f"{self.base_url}/api/auth/login",
                f"{self.base_url}/login",
                f"{self.base_url}/auth/login"
            ]

            # Try each endpoint
            for endpoint in possible_login_endpoints:
                try:
                    print(f"Trying login endpoint: {endpoint}")
                    login_response = self.session.post(endpoint, json=login_data)

                    if login_response.status_code == 200:
                        response_data = login_response.json() if login_response.content else {}
                        if 'token' in response_data or 'success' in response_data:
                            print("Login successful!")
                            return True, "Login successful"

                except Exception as e:
                    print(f"Failed to try endpoint {endpoint}: {str(e)}")
                    continue

            # If API endpoints don't work, try form-based login
            print("Trying form-based login...")
            login_response = self.session.post(self.base_url, data=login_data)

            # Check if login was successful by looking for redirect or dashboard content
            if 'dashboard' in login_response.url.lower() or login_response.status_code == 200:
                return True, "Login successful"
            else:
                return False, f"Login failed. Status code: {login_response.status_code}"

        except Exception as e:
            return False, f"Login error: {str(e)}"

    def search_deal(self, deal_number):
        """
        Search for a deal by deal number
        """
        try:
            # Navigate to dashboard first
            dashboard_response = self.session.get(f"{self.base_url}/#!/dashboard/")

            if dashboard_response.status_code != 200:
                return False, "Failed to access dashboard", []

            # Try different search endpoints
            search_endpoints = [
                f"{self.base_url}/api/search",
                f"{self.base_url}/api/deals/search",
                f"{self.base_url}/search"
            ]

            search_data = {
                'dealNumber': deal_number,
                'query': deal_number,
                'searchTerm': deal_number
            }

            for endpoint in search_endpoints:
                try:
                    print(f"Trying search endpoint: {endpoint}")
                    search_response = self.session.post(endpoint, json=search_data)

                    if search_response.status_code == 200:
                        try:
                            results = search_response.json()
                            if results and isinstance(results, (list, dict)):
                                return True, "Search successful", results
                        except:
                            # If not JSON, try to parse HTML
                            soup = BeautifulSoup(search_response.text, 'html.parser')
                            # Extract deal information from HTML
                            deals = self._extract_deals_from_html(soup)
                            if deals:
                                return True, "Search successful", deals

                except Exception as e:
                    print(f"Failed to try search endpoint {endpoint}: {str(e)}")
                    continue

            # Try GET request with query parameter
            get_search_url = f"{self.base_url}/api/search?q={deal_number}"
            search_response = self.session.get(get_search_url)

            if search_response.status_code == 200:
                try:
                    results = search_response.json()
                    return True, "Search successful", results
                except:
                    return False, "Search returned non-JSON response", []

            return False, "No search endpoint worked", []

        except Exception as e:
            return False, f"Search error: {str(e)}", []

    def _extract_deals_from_html(self, soup):
        """
        Extract deal information from HTML response
        """
        deals = []

        # Look for common table structures or deal containers
        tables = soup.find_all('table')
        for table in tables:
            rows = table.find_all('tr')
            for row in rows[1:]:  # Skip header row
                cells = row.find_all(['td', 'th'])
                if len(cells) >= 3:  # Assuming at least 3 columns
                    deal_info = {
                        'deal_number': cells[0].get_text(strip=True) if cells[0] else '',
                        'deal_type': cells[1].get_text(strip=True) if len(cells) > 1 else '',
                        'status': cells[2].get_text(strip=True) if len(cells) > 2 else '',
                        'customer': cells[3].get_text(strip=True) if len(cells) > 3 else '',
                    }
                    deals.append(deal_info)

        return deals

def lambda_handler(event, context):
    """
    AWS Lambda handler function
    """
    try:
        # Extract parameters from event
        deal_number = event.get('deal_number', '')
        username = event.get('username', 'abi34343@232')
        password = event.get('password', 'ddokeee')

        if not deal_number:
            return {
                'statusCode': 400,
                'body': json.dumps({
                    'error': 'deal_number is required',
                    'message': 'Please provide a deal_number in the event payload'
                })
            }

        # Initialize the portal client
        portal = GeorgiaDLRPortal()

        # Login
        print(f"Attempting to login with username: {username}")
        login_success, login_message = portal.login(username, password)

        if not login_success:
            return {
                'statusCode': 401,
                'body': json.dumps({
                    'error': 'Login failed',
                    'message': login_message
                })
            }

        # Search for deal
        print(f"Searching for deal number: {deal_number}")
        search_success, search_message, deals = portal.search_deal(deal_number)

        if not search_success:
            return {
                'statusCode': 404,
                'body': json.dumps({
                    'error': 'Search failed',
                    'message': search_message
                })
            }

        # Return successful response
        return {
            'statusCode': 200,
            'body': json.dumps({
                'success': True,
                'message': search_message,
                'deal_number': deal_number,
                'deals_found': len(deals) if isinstance(deals, list) else 1,
                'deals': deals
            })
        }

    except Exception as e:
        print(f"Lambda execution error: {str(e)}")
        return {
            'statusCode': 500,
            'body': json.dumps({
                'error': 'Internal server error',
                'message': str(e)
            })
        }