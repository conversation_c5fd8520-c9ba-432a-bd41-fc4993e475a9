#!/usr/bin/env python3
"""
Deployment script for Georgia DLR Portal Lambda function
"""

import os
import zipfile
import subprocess
import sys

def install_dependencies():
    """
    Install dependencies in a local directory for packaging
    """
    print("Installing dependencies...")
    
    # Create a directory for dependencies
    if not os.path.exists('package'):
        os.makedirs('package')
    
    # Install dependencies
    subprocess.run([
        sys.executable, '-m', 'pip', 'install', 
        '-r', 'requirements.txt', 
        '-t', 'package'
    ], check=True)
    
    print("Dependencies installed successfully!")

def create_deployment_package():
    """
    Create a deployment package (ZIP file) for AWS Lambda
    """
    print("Creating deployment package...")
    
    # Create ZIP file
    with zipfile.ZipFile('lambda_deployment.zip', 'w', zipfile.ZIP_DEFLATED) as zipf:
        # Add the main lambda function
        zipf.write('lambda_function.py')
        
        # Add all dependencies from package directory
        for root, dirs, files in os.walk('package'):
            for file in files:
                file_path = os.path.join(root, file)
                arcname = os.path.relpath(file_path, 'package')
                zipf.write(file_path, arcname)
    
    print("Deployment package created: lambda_deployment.zip")
    
    # Get file size
    size = os.path.getsize('lambda_deployment.zip')
    print(f"Package size: {size / (1024*1024):.2f} MB")
    
    if size > 50 * 1024 * 1024:  # 50MB limit for direct upload
        print("⚠️  Warning: Package is larger than 50MB. You'll need to upload to S3 first.")
    else:
        print("✅ Package size is within direct upload limits.")

def cleanup():
    """
    Clean up temporary files
    """
    print("Cleaning up...")
    
    # Remove package directory
    if os.path.exists('package'):
        import shutil
        shutil.rmtree('package')
    
    print("Cleanup completed!")

def main():
    """
    Main deployment function
    """
    print("🚀 Georgia DLR Portal Lambda Deployment")
    print("=" * 50)
    
    try:
        # Install dependencies
        install_dependencies()
        
        # Create deployment package
        create_deployment_package()
        
        print("\n✅ Deployment package ready!")
        print("\nNext steps:")
        print("1. Upload lambda_deployment.zip to AWS Lambda")
        print("2. Set the handler to: lambda_function.lambda_handler")
        print("3. Configure environment variables if needed")
        print("4. Set appropriate timeout (recommend 30+ seconds)")
        print("5. Test with a sample event")
        
        print("\nSample test event:")
        print("""{
  "deal_number": "12345",
  "username": "abi34343@232",
  "password": "ddokeee"
}""")
        
    except Exception as e:
        print(f"❌ Deployment failed: {str(e)}")
        return 1
    
    finally:
        # Always cleanup
        cleanup()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
