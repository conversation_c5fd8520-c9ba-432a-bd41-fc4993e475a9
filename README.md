# Georgia DLR Portal Lambda Function

This AWS Lambda function automates the process of logging into the Georgia DLR portal and searching for deals by deal number.

## Features

- Automated login to https://georgia.dlrdmv.com
- Session management with proper headers
- Deal search functionality
- Error handling and logging
- JSON response format

## Files

- `lambda_function.py` - Main Lambda function code
- `requirements.txt` - Python dependencies
- `test_lambda.py` - Local testing script
- `deploy.py` - Deployment helper script
- `README.md` - This documentation

## Setup Instructions

### 1. Local Testing

First, install dependencies locally:

```bash
pip install -r requirements.txt
```

Test the function locally:

```bash
python test_lambda.py
```

### 2. AWS Lambda Deployment

#### Option A: Using the deployment script

```bash
python deploy.py
```

This will create `lambda_deployment.zip` ready for upload to AWS Lambda.

#### Option B: Manual deployment

1. Install dependencies in a package directory:
   ```bash
   pip install -r requirements.txt -t package/
   ```

2. Create a ZIP file with your code and dependencies:
   ```bash
   # Copy lambda_function.py to package directory
   cp lambda_function.py package/
   
   # Create ZIP file
   cd package && zip -r ../lambda_deployment.zip . && cd ..
   ```

### 3. AWS Lambda Configuration

1. **Create a new Lambda function** in AWS Console
2. **Upload the ZIP file** (`lambda_deployment.zip`)
3. **Set the handler** to: `lambda_function.lambda_handler`
4. **Configure runtime**: Python 3.9 or later
5. **Set timeout**: 30+ seconds (web scraping can take time)
6. **Memory**: 256 MB or higher

### 4. Testing in AWS Lambda

Use this test event in the AWS Lambda console:

```json
{
  "deal_number": "12345",
  "username": "abi34343@232",
  "password": "ddokeee"
}
```

## Usage

### Input Event Format

```json
{
  "deal_number": "string (required)",
  "username": "string (optional, defaults to abi34343@232)",
  "password": "string (optional, defaults to ddokeee)"
}
```

### Response Format

#### Success Response (200)

```json
{
  "statusCode": 200,
  "body": {
    "success": true,
    "message": "Search successful",
    "deal_number": "12345",
    "deals_found": 1,
    "deals": [
      {
        "deal_number": "12345",
        "deal_type": "ETR",
        "status": "Pending",
        "customer": "John Doe"
      }
    ]
  }
}
```

#### Error Responses

**Missing deal_number (400)**
```json
{
  "statusCode": 400,
  "body": {
    "error": "deal_number is required",
    "message": "Please provide a deal_number in the event payload"
  }
}
```

**Login failed (401)**
```json
{
  "statusCode": 401,
  "body": {
    "error": "Login failed",
    "message": "Invalid credentials or login endpoint not found"
  }
}
```

**Search failed (404)**
```json
{
  "statusCode": 404,
  "body": {
    "error": "Search failed",
    "message": "No deals found or search endpoint not accessible"
  }
}
```

## Important Notes

### Security Considerations

1. **Credentials**: Consider using AWS Secrets Manager or environment variables instead of hardcoding credentials
2. **Rate Limiting**: The portal may have rate limits - implement delays if needed
3. **Session Management**: Sessions are maintained per Lambda execution

### Troubleshooting

1. **Login Issues**: The portal might be using different authentication methods (OAuth, CSRF tokens, etc.)
2. **Search Issues**: The search API endpoints might be different than expected
3. **Timeout**: Increase Lambda timeout if the portal is slow to respond

### Potential Improvements

1. **Environment Variables**: Store credentials in environment variables
2. **Caching**: Implement session caching for multiple searches
3. **Retry Logic**: Add retry mechanisms for failed requests
4. **Logging**: Enhanced logging for debugging
5. **Error Handling**: More specific error handling for different scenarios

## API Endpoint Discovery

Since this is a web scraping solution, you might need to:

1. **Inspect Network Traffic**: Use browser developer tools to find actual API endpoints
2. **Check for CSRF Tokens**: Some portals require CSRF tokens for authentication
3. **Handle JavaScript**: If the portal heavily relies on JavaScript, consider using Selenium

## Legal Considerations

- Ensure you have permission to automate access to the portal
- Respect the portal's terms of service
- Implement appropriate rate limiting to avoid overloading the server
- Consider reaching out to the portal administrators for official API access
